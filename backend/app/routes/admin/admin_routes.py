from flask import Blueprint, jsonify, request
from app.extensions import mongo
from bson import ObjectId
from app.utils.decorators import admin_required
from app.utils.hash_utils import hash_password_custom
from app.utils.helpers import (
    is_valid_name, is_valid_date, is_strong_password,
    is_valid_email, is_valid_username
)
from datetime import datetime

admin_bp = Blueprint("admin_bp", __name__, url_prefix="/admin")

# 1. Lister tous les utilisateurs
@admin_bp.route("/users", methods=["GET"])
@admin_required
def get_all_users():
    users = mongo.db.users.find({}, {"password": 0})
    result = []
    for user in users:
        user["_id"] = str(user["_id"])
        result.append(user)
    return jsonify(result), 200

# 2. Créer un nouvel utilisateur
@admin_bp.route("/users", methods=["POST"])
@admin_required
def create_user():
    data = request.get_json()
    required = ["first_name", "last_name", "date_of_birth", "username", "gender", "email", "password"]
    if not all(field in data for field in required):
        return jsonify({"msg": "Missing required fields"}), 400

    if not is_valid_name(data["first_name"]) or not is_valid_name(data["last_name"]):
        return jsonify({"msg": "Invalid name format"}), 400
    if not is_valid_date(data["date_of_birth"]):
        return jsonify({"msg": "Date must be DD/MM/YYYY"}), 400
    if not is_valid_username(data["username"]):
        return jsonify({"msg": "Invalid username format"}), 400
    if not is_valid_email(data["email"]):
        return jsonify({"msg": "Invalid email format"}), 400
    if not is_strong_password(data["password"]):
        return jsonify({"msg": "Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un symbole"}), 400

    if mongo.db.users.find_one({"email": data["email"]}) or mongo.db.users.find_one({"username": data["username"]}):
        return jsonify({"msg": "Email or username already exists"}), 400

    user = {
        "first_name": data["first_name"],
        "last_name": data["last_name"],
        "date_of_birth": data["date_of_birth"],
        "username": data["username"],
        "gender": data["gender"],
        "email": data["email"],
        "password": hash_password_custom(data["password"]),
        "role": data.get("role", "user"),
        "banned": False,
        "active": True,
        "created_at": datetime.utcnow()
    }

    mongo.db.users.insert_one(user)
    return jsonify({"msg": "User created successfully"}), 201

# 3. Voir un utilisateur par ID
@admin_bp.route("/users/<user_id>", methods=["GET"])
@admin_required
def get_user(user_id):
    user = mongo.db.users.find_one({"_id": ObjectId(user_id)}, {"password": 0})
    if not user:
        return jsonify({"msg": "User not found"}), 404
    user["_id"] = str(user["_id"])
    return jsonify(user), 200

# 4. Modifier un utilisateur (profil + mot de passe + rôle)
@admin_bp.route("/users/<user_id>", methods=["PUT"])
@admin_required
def update_user(user_id):
    data = request.get_json()
    update_fields = {}

    for field in ["first_name", "last_name", "gender", "date_of_birth", "username", "email"]:
        if field in data:
            update_fields[field] = data[field]

    if "role" in data:
        if data["role"] in ["user", "admin"]:
            update_fields["role"] = data["role"]
        else:
            return jsonify({"msg": "Invalid role value"}), 400

    if "password" in data:
        if not is_strong_password(data["password"]):
            return jsonify({"msg": "Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un symbole"}), 400
        update_fields["password"] = hash_password_custom(data["password"])

    if not update_fields:
        return jsonify({"msg": "No valid fields to update"}), 400

    result = mongo.db.users.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": update_fields}
    )

    if result.matched_count == 0:
        return jsonify({"msg": "User not found"}), 404

    return jsonify({"msg": "User updated successfully"}), 200

# 5. Supprimer un utilisateur
@admin_bp.route("/users/<user_id>", methods=["DELETE"])
@admin_required
def delete_user(user_id):
    result = mongo.db.users.delete_one({"_id": ObjectId(user_id)})
    if result.deleted_count == 0:
        return jsonify({"msg": "User not found"}), 404
    return jsonify({"msg": "User deleted"}), 200

# 6. Bannir un utilisateur
@admin_bp.route("/users/<user_id>/ban", methods=["PUT"])
@admin_required
def ban_user(user_id):
    result = mongo.db.users.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": {"banned": True}}
    )
    if result.matched_count == 0:
        return jsonify({"msg": "User not found"}), 404
    return jsonify({"msg": "User has been banned"}), 200

# 7. Débannir un utilisateur
@admin_bp.route("/users/<user_id>/unban", methods=["PUT"])
@admin_required
def unban_user(user_id):
    result = mongo.db.users.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": {"banned": False}}
    )
    if result.matched_count == 0:
        return jsonify({"msg": "User not found"}), 404
    return jsonify({"msg": "User has been unbanned"}), 200
