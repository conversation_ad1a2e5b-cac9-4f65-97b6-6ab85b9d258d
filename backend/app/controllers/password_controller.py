from flask import request, jsonify
from flask_jwt_extended import (
    create_access_token,
    verify_jwt_in_request,
    get_jwt,
    get_jwt_identity
)
from datetime import timedelta
from ..extensions import mongo, mail
from ..utils.helpers import is_strong_password
from ..utils.hash_utils import hash_password_custom
from flask_mail import Message


def send_reset_link():
    data = request.get_json()
    email = data.get("email")

    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"msg": "User not found"}), 404

    token = create_access_token(
        identity=email,
        expires_delta=timedelta(minutes=10),
        additional_claims={"reset": True}
    )

    reset_link = f"http://localhost:5173/reset-password?token={token}"

    msg = Message(
        subject="PICA - Password Reset",
        sender="<EMAIL>",
        recipients=[email],
        body=(
            f"Hello,\n\n"
            f"To reset your password, click the link below:\n{reset_link}\n\n"
            f"This link will expire in 10 minutes.\n\n"
            f"If you didn't request this, you can ignore this email.\n\n"
            f"— PICA Security Team"
        )
    )
    mail.send(msg)

    return jsonify({"msg": "Reset link sent to email"}), 200


def reset_password():
    try:
        verify_jwt_in_request()
    except Exception:
        return jsonify({"msg": "Invalid or expired token"}), 401

    claims = get_jwt()
    if not claims.get("reset"):
        return jsonify({"msg": "Unauthorized action"}), 403

    email = get_jwt_identity()
    data = request.get_json()
    new_password = data.get("new_password")

    if not is_strong_password(new_password):
        return jsonify({"msg": "Weak password"}), 400

    hashed = hash_password_custom(new_password)
    result = mongo.db.users.update_one(
        {"email": email},
        {"$set": {"password": hashed}}
    )

    if result.modified_count == 1:
        return jsonify({"msg": "Password successfully reset"}), 200
    return jsonify({"msg": "Failed to reset password"}), 500
