# Framework Web
Flask==2.3.3
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3
Flask-RESTful==0.3.10

# Base de données
pymongo==4.5.0
mongoengine==0.27.0

# Sécurité et Cryptographie
cryptography==41.0.4
bcrypt==4.0.1
passlib==1.7.4

# Intégrations outils sécurité
python-nmap==0.7.1
requests==2.31.0
beautifulsoup4==4.12.2
lxml==4.9.3



# Monitoring et logs
python-logstash==0.4.8
structlog==23.1.0

# Utilitaires
python-dotenv==1.0.0
celery==5.3.1
redis==4.6.0
schedule==1.2.0

# Tests
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0

# Documentation
sphinx==7.1.2

# Validation
marshmallow==3.20.1
email-validator==2.0.0

# HTTP et API
httpx==0.24.1

aiohttp==3.9.1

# Analyse de fichiers

# Détection phishing
tldextract==3.4.4
whois==0.9.27

# Analyse réseau
scapy==2.5.0
netaddr==0.8.0

# Configuration
pyyaml==6.0.1
configparser==6.0.0

# Sérialisation
jsonschema==4.19.0
msgpack==1.0.5

# Gestion des tâches
apscheduler==3.10.4

# Métriques et monitoring
prometheus-client==0.17.1
psutil==5.9.5