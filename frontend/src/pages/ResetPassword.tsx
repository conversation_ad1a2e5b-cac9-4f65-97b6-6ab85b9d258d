import React, { useState, useEffect } from 'react';
import { Lock, AlertCircle, ShieldCheck, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import axios from 'axios';

export default function ResetPassword() {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setError('Token de réinitialisation manquant');
    }
  }, [token]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (password !== confirmPassword) {
      setError('Les mots de passe ne correspondent pas');
      return;
    }

    if (password.length < 8) {
      setError('Le mot de passe doit contenir au moins 8 caractères');
      return;
    }

    if (!token) {
      setError('Token de réinitialisation manquant');
      return;
    }

    setIsLoading(true);

    try {
      const res = await axios.post('http://localhost:5000/auth/reset-password', {
        new_password: password,
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      setSuccess(res.data.msg || 'Mot de passe réinitialisé avec succès');
      
      // Rediriger vers la page de connexion après 3 secondes
      setTimeout(() => navigate('/login'), 3000);
    } catch (err: any) {
      const msg = err?.response?.data?.msg || 'Erreur lors de la réinitialisation du mot de passe';
      setError(msg);
    } finally {
      setIsLoading(false);
    }
  };

  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#2d004d] to-[#5f1a7c] px-4">
        <div className="max-w-md w-full bg-[#1a1a2e] rounded-lg p-8 shadow-xl text-white text-center">
          <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-2 text-red-400">Lien invalide</h2>
          <p className="text-sm text-gray-300 mb-6">Le lien de réinitialisation est manquant ou invalide.</p>
          <button
            onClick={() => navigate('/forgot-password')}
            className="px-6 py-2 bg-violet-600 hover:bg-violet-700 rounded-md transition-colors"
          >
            Demander un nouveau lien
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#2d004d] to-[#5f1a7c] px-4">
      <div className="max-w-md w-full bg-[#1a1a2e] rounded-lg p-8 shadow-xl text-white">
        <div className="text-center mb-8">
          <ShieldCheck className="w-12 h-12 text-violet-400 mx-auto mb-2" />
          <h2 className="text-3xl font-bold">Nouveau mot de passe</h2>
          <p className="text-sm text-violet-200">
            Choisissez un nouveau mot de passe sécurisé
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-400 rounded-md flex gap-2 items-center text-red-300">
            <AlertCircle size={16} />
            <span className="text-sm">{error}</span>
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-500/10 border border-green-400 rounded-md flex gap-2 items-center text-green-300">
            <CheckCircle size={16} />
            <span className="text-sm">{success}</span>
            <p className="text-xs text-violet-300 mt-2">Redirection vers la connexion dans 3 secondes...</p>
          </div>
        )}

        <form className="space-y-4" onSubmit={handleSubmit}>
          <div>
            <label className="block text-sm mb-1">Nouveau mot de passe</label>
            <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
              <Lock className="w-4 h-4 text-violet-400 mr-2" />
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full bg-transparent text-white focus:outline-none"
                placeholder="********"
                required
                disabled={isLoading || !!success}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="ml-2 text-violet-400 hover:text-violet-300 focus:outline-none"
                disabled={isLoading || !!success}
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm mb-1">Confirmer le mot de passe</label>
            <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
              <Lock className="w-4 h-4 text-violet-400 mr-2" />
              <input
                type={showConfirmPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full bg-transparent text-white focus:outline-none"
                placeholder="********"
                required
                disabled={isLoading || !!success}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="ml-2 text-violet-400 hover:text-violet-300 focus:outline-none"
                disabled={isLoading || !!success}
              >
                {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading || !!success}
            className="w-full py-2 rounded-md bg-gradient-to-r from-violet-500 to-indigo-500 hover:opacity-90 transition duration-200 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Réinitialisation...' : 'Réinitialiser le mot de passe'}
          </button>
        </form>

        <p className="mt-8 text-center text-xs text-violet-400">
          © PICA - Plateforme de cybersécurité
        </p>
      </div>
    </div>
  );
}
