import React, { useState } from 'react';
import {
  Mail,
  Lock,
  User,
  CalendarDays,
  AlertCircle,
  ShieldCheck
} from 'lucide-react';
import { Link } from 'react-router-dom';
import axios from 'axios';

export default function Register() {
  const [firstname, setFirstname] = useState<string>('');
  const [lastname, setLastname] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [birthdate, setBirthdate] = useState<string>('');
  const [username, setUsername] = useState<string>('');
  const [gender, setGender] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    try {
      const formatDate = (dateStr: string) => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      };

      const res = await axios.post('http://localhost:5000/auth/register', {
        first_name: firstname,
        last_name: lastname,
        date_of_birth: formatDate(birthdate),
        username,
        gender: gender || 'Not specified',
        email,
        password,
      });
      setSuccess(res.data?.msg || 'Inscription réussie. Veuillez confirmer votre adresse e-mail.');
    } catch (err: any) {
      const msg = err?.response?.data?.msg || 'Erreur lors de l’inscription';
      setError(msg);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#2d004d] to-[#5f1a7c] flex items-center justify-center relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/background-waves.svg')] bg-cover opacity-10 pointer-events-none" />
      <div className="flex flex-col md:flex-row w-full max-w-6xl mx-auto items-center justify-between px-6 md:px-12">

        <div className="hidden md:flex w-1/2 justify-center items-center">
          <img src="/cyber-shield.png" alt="Cybershield" className="w-80 animate-pulse drop-shadow-lg" />
        </div>

        <div className="w-full md:w-1/2 bg-[#1a1a2e]/80 backdrop-blur-md rounded-lg p-8 shadow-2xl text-white">
          <div className="text-center mb-8">
            <ShieldCheck className="w-12 h-12 text-violet-400 mx-auto mb-2" />
            <h2 className="text-3xl font-bold">Créer un compte</h2>
            <p className="text-sm text-violet-200">Rejoignez la plateforme PICA</p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-500/10 border border-red-400 rounded-md flex gap-2 items-center text-red-300">
              <AlertCircle size={16} />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {success && (
            <div className="mb-4 p-3 bg-green-500/10 border border-green-400 rounded-md text-green-300 text-sm">
              {success}
            </div>
          )}

          <form className="space-y-4" onSubmit={handleSubmit}>
            <div className="flex gap-2">
              <div className="w-1/2">
                <label className="block text-sm mb-1">Prénom</label>
                <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                  <User className="w-4 h-4 text-violet-400 mr-2" />
                  <input type="text" value={firstname} onChange={(e) => setFirstname(e.target.value)}
                    className="w-full bg-transparent text-white focus:outline-none" placeholder="Prénom" required />
                </div>
              </div>
              <div className="w-1/2">
                <label className="block text-sm mb-1">Nom</label>
                <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                  <User className="w-4 h-4 text-violet-400 mr-2" />
                  <input type="text" value={lastname} onChange={(e) => setLastname(e.target.value)}
                    className="w-full bg-transparent text-white focus:outline-none" placeholder="Nom" required />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm mb-1">Nom d'utilisateur</label>
              <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                <User className="w-4 h-4 text-violet-400 mr-2" />
                <input type="text" value={username} onChange={(e) => setUsername(e.target.value)}
                  className="w-full bg-transparent text-white focus:outline-none" placeholder="nom_utilisateur" required />
              </div>
            </div>

            <div>
              <label className="block text-sm mb-1">Adresse e-mail</label>
              <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                <Mail className="w-4 h-4 text-violet-400 mr-2" />
                <input type="email" value={email} onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-transparent text-white focus:outline-none" placeholder="<EMAIL>" required />
              </div>
            </div>

            <div>
              <label className="block text-sm mb-1">Mot de passe</label>
              <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                <Lock className="w-4 h-4 text-violet-400 mr-2" />
                <input type="password" value={password} onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-transparent text-white focus:outline-none" placeholder="********" required />
              </div>
            </div>

            <div>
              <label className="block text-sm mb-1">Date de naissance</label>
              <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                <CalendarDays className="w-4 h-4 text-violet-400 mr-2" />
                <input type="date" value={birthdate} onChange={(e) => setBirthdate(e.target.value)}
                  className="w-full bg-transparent text-white focus:outline-none" required />
              </div>
            </div>

            <div>
              <label className="block text-sm mb-1">Genre</label>
              <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                <User className="w-4 h-4 text-violet-400 mr-2" />
                <select value={gender} onChange={(e) => setGender(e.target.value)}
                  className="w-full bg-transparent text-white focus:outline-none" required>
                  <option value="" className="bg-[#2d2d44] text-white">Sélectionnez votre genre</option>
                  <option value="Male" className="bg-[#2d2d44] text-white">Homme</option>
                  <option value="Female" className="bg-[#2d2d44] text-white">Femme</option>
                  <option value="Other" className="bg-[#2d2d44] text-white">Autre</option>
                  <option value="Prefer not to say" className="bg-[#2d2d44] text-white">Préfère ne pas dire</option>
                </select>
              </div>
            </div>

            <button type="submit"
              className="w-full py-2 rounded-md bg-gradient-to-r from-violet-500 to-indigo-500 hover:opacity-90 transition duration-200 text-white font-semibold">
              S'inscrire
            </button>
          </form>

          <p className="text-center text-sm text-violet-300 mt-6">
            Vous avez déjà un compte ? <Link to="/login" className="underline hover:text-white">Se connecter</Link>
          </p>
        </div>
      </div>
    </div>
  );
}
