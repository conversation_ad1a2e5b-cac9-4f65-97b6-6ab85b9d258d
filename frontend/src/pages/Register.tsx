import React, { useState } from 'react';
import {
  Mail,
  Lock,
  User,
  CalendarDays,
  AlertCircle,
  Shield,
  UserPlus,
  CheckCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import axios from 'axios';

export default function Register() {
  const [firstname, setFirstname] = useState<string>('');
  const [lastname, setLastname] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [birthdate, setBirthdate] = useState<string>('');
  const [username, setUsername] = useState<string>('');
  const [gender, setGender] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    try {
      const formatDate = (dateStr: string) => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      };

      const res = await axios.post('http://localhost:5000/auth/register', {
        first_name: firstname,
        last_name: lastname,
        date_of_birth: formatDate(birthdate),
        username,
        gender: gender || 'Not specified',
        email,
        password,
      });
      setSuccess(res.data?.msg || 'Inscription réussie. Veuillez confirmer votre adresse e-mail.');
    } catch (err: any) {
      const msg = err?.response?.data?.msg || 'Erreur lors de l’inscription';
      setError(msg);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Animated background waves */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="none">
            <defs>
              <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#A855F7" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#7C3AED" stopOpacity="0.1" />
              </linearGradient>
            </defs>
            <path d="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z" fill="url(#wave-gradient)">
              <animate attributeName="d"
                values="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z;
                        M0,300 C300,500 600,100 1200,400 L1200,800 L0,800 Z;
                        M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z"
                dur="8s" repeatCount="indefinite" />
            </path>
          </svg>
        </div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 opacity-30">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
        </div>
        <div className="absolute top-40 right-32 opacity-25">
          <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
        <div className="absolute bottom-32 left-16 opacity-20">
          <div className="w-4 h-4 bg-purple-200 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>
        <div className="absolute bottom-20 right-20 opacity-35">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Gradient orbs */}
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex">
        {/* Left side - Registration form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* Registration card */}
            <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
              {/* Header */}
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-4 shadow-lg">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-white mb-2">Créer un compte</h1>
                <p className="text-purple-200">Rejoignez la plateforme PICA</p>
              </div>

              {/* Error message */}
              {error && (
                <div className="mb-4 p-4 bg-red-500/10 border border-red-400/30 rounded-xl flex items-center space-x-3 text-red-300">
                  <AlertCircle className="w-5 h-5 flex-shrink-0" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {/* Success message */}
              {success && (
                <div className="mb-4 p-4 bg-green-500/10 border border-green-400/30 rounded-xl flex items-center space-x-3 text-green-300">
                  <CheckCircle className="w-5 h-5 flex-shrink-0" />
                  <span className="text-sm">{success}</span>
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Name fields */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-purple-200 mb-1">Prénom</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="w-4 h-4 text-purple-400" />
                      </div>
                      <input
                        type="text"
                        value={firstname}
                        onChange={(e) => setFirstname(e.target.value)}
                        className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="Prénom"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-purple-200 mb-1">Nom</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="w-4 h-4 text-purple-400" />
                      </div>
                      <input
                        type="text"
                        value={lastname}
                        onChange={(e) => setLastname(e.target.value)}
                        className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="Nom"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Username field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Nom d'utilisateur</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <UserPlus className="w-4 h-4 text-purple-400" />
                    </div>
                    <input
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="nom_utilisateur"
                      required
                    />
                  </div>
                </div>

                {/* Email field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Adresse e-mail</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="w-4 h-4 text-purple-400" />
                    </div>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                {/* Password field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Mot de passe</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="w-4 h-4 text-purple-400" />
                    </div>
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="••••••••••"
                      required
                    />
                  </div>
                </div>

                {/* Birth date field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Date de naissance</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <CalendarDays className="w-4 h-4 text-purple-400" />
                    </div>
                    <input
                      type="date"
                      value={birthdate}
                      onChange={(e) => setBirthdate(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      required
                    />
                  </div>
                </div>

                {/* Gender field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Genre</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="w-4 h-4 text-purple-400" />
                    </div>
                    <select
                      value={gender}
                      onChange={(e) => setGender(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      required
                    >
                      <option value="" className="bg-purple-900 text-white">Sélectionnez votre genre</option>
                      <option value="Male" className="bg-purple-900 text-white">Homme</option>
                      <option value="Female" className="bg-purple-900 text-white">Femme</option>
                      <option value="Other" className="bg-purple-900 text-white">Autre</option>
                      <option value="Prefer not to say" className="bg-purple-900 text-white">Préfère ne pas dire</option>
                    </select>
                  </div>
                </div>

                {/* Submit button */}
                <button
                  type="submit"
                  className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                >
                  S'inscrire
                </button>
              </form>

              {/* Login link */}
              <div className="mt-6 text-center">
                <p className="text-purple-200">
                  Vous avez déjà un compte ?{' '}
                  <Link
                    to="/login"
                    className="text-purple-300 hover:text-white font-medium transition-colors"
                  >
                    Se connecter
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Illustration */}
        <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-12">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Giant User Creation Shield */}
            <div className="relative">
              {/* Main Giant Shield */}
              <div className="w-80 h-96 bg-gradient-to-br from-green-500 via-emerald-600 to-cyan-700 rounded-t-full rounded-b-3xl shadow-[0_0_80px_rgba(34,197,94,0.6)] border-4 border-green-300/30">
                {/* Inner shield glow */}
                <div className="absolute inset-6 bg-gradient-to-br from-green-400/20 via-cyan-400/10 to-transparent rounded-t-full rounded-b-3xl"></div>

                {/* Shield pattern */}
                <div className="absolute inset-8 border-2 border-green-300/20 rounded-t-full rounded-b-3xl">
                  <div className="absolute inset-6 border border-green-300/10 rounded-t-full rounded-b-3xl"></div>
                </div>

                {/* Central massive user plus icon */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-br from-cyan-400 via-blue-500 to-green-600 rounded-3xl flex items-center justify-center shadow-[0_0_40px_rgba(6,182,212,0.8)] animate-pulse">
                  <UserPlus className="w-20 h-20 text-white drop-shadow-lg" />
                </div>

                {/* Decorative elements */}
                <div className="absolute top-12 left-1/2 transform -translate-x-1/2 w-32 h-2 bg-gradient-to-r from-transparent via-green-200/60 to-transparent rounded-full"></div>
                <div className="absolute top-16 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-green-200/40 to-transparent rounded-full"></div>
                <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 w-28 h-1.5 bg-gradient-to-r from-transparent via-green-200/50 to-transparent rounded-full"></div>
              </div>

              {/* Floating user creation icons around the shield */}
              <div className="absolute -top-8 -left-8 w-20 h-20 bg-gradient-to-br from-blue-400 to-cyan-600 rounded-2xl flex items-center justify-center shadow-[0_0_30px_rgba(59,130,246,0.6)] animate-float">
                <User className="w-12 h-12 text-white" />
              </div>

              <div className="absolute -top-4 -right-12 w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-600 rounded-xl flex items-center justify-center shadow-[0_0_25px_rgba(168,85,247,0.6)] animate-float" style={{ animationDelay: '1s' }}>
                <Mail className="w-10 h-10 text-white" />
              </div>

              <div className="absolute -bottom-8 -right-8 w-24 h-24 bg-gradient-to-br from-orange-400 to-yellow-500 rounded-2xl flex items-center justify-center shadow-[0_0_35px_rgba(251,146,60,0.6)] animate-float" style={{ animationDelay: '2s' }}>
                <CheckCircle className="w-14 h-14 text-white" />
              </div>

              <div className="absolute bottom-1/3 -left-16 w-18 h-18 bg-gradient-to-br from-emerald-400 to-green-500 rounded-xl flex items-center justify-center shadow-[0_0_28px_rgba(34,197,94,0.6)] animate-float" style={{ animationDelay: '0.5s' }}>
                <Lock className="w-10 h-10 text-white" />
              </div>

              {/* Rotating energy rings */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] border-4 border-green-300/20 rounded-full animate-spin" style={{ animationDuration: '30s' }}>
                <div className="absolute inset-12 border-2 border-cyan-300/15 rounded-full animate-spin" style={{ animationDuration: '20s', animationDirection: 'reverse' }}></div>
                <div className="absolute inset-24 border border-emerald-300/10 rounded-full animate-spin" style={{ animationDuration: '15s' }}></div>
              </div>

              {/* Glowing base */}
              <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 w-96 h-8 bg-gradient-to-r from-transparent via-green-400/60 to-transparent rounded-full blur-2xl"></div>
            </div>

            {/* Background particles */}
            <div className="absolute top-16 left-16 w-4 h-4 bg-green-400 rounded-full animate-ping shadow-[0_0_15px_rgba(34,197,94,0.8)]"></div>
            <div className="absolute top-32 right-20 w-3 h-3 bg-cyan-400 rounded-full animate-ping shadow-[0_0_12px_rgba(6,182,212,0.8)]" style={{ animationDelay: '0.5s' }}></div>
            <div className="absolute bottom-32 left-24 w-5 h-5 bg-blue-400 rounded-full animate-ping shadow-[0_0_18px_rgba(59,130,246,0.8)]" style={{ animationDelay: '1s' }}></div>
            <div className="absolute bottom-20 right-16 w-3 h-3 bg-purple-400 rounded-full animate-ping shadow-[0_0_12px_rgba(168,85,247,0.8)]" style={{ animationDelay: '1.5s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
}
