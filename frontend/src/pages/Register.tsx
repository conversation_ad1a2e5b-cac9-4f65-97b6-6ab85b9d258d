import React, { useState } from 'react';
import {
  Mail,
  Lock,
  User,
  CalendarDays,
  AlertCircle,
  Shield,
  UserPlus,
  CheckCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import axios from 'axios';

export default function Register() {
  const [firstname, setFirstname] = useState<string>('');
  const [lastname, setLastname] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [birthdate, setBirthdate] = useState<string>('');
  const [username, setUsername] = useState<string>('');
  const [gender, setGender] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    try {
      const formatDate = (dateStr: string) => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      };

      const res = await axios.post('http://localhost:5000/auth/register', {
        first_name: firstname,
        last_name: lastname,
        date_of_birth: formatDate(birthdate),
        username,
        gender: gender || 'Not specified',
        email,
        password,
      });
      setSuccess(res.data?.msg || 'Inscription réussie. Veuillez confirmer votre adresse e-mail.');
    } catch (err: any) {
      const msg = err?.response?.data?.msg || 'Erreur lors de l’inscription';
      setError(msg);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Animated background waves */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="none">
            <defs>
              <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#A855F7" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#7C3AED" stopOpacity="0.1" />
              </linearGradient>
            </defs>
            <path d="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z" fill="url(#wave-gradient)">
              <animate attributeName="d"
                values="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z;
                        M0,300 C300,500 600,100 1200,400 L1200,800 L0,800 Z;
                        M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z"
                dur="8s" repeatCount="indefinite" />
            </path>
          </svg>
        </div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 opacity-30">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
        </div>
        <div className="absolute top-40 right-32 opacity-25">
          <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
        <div className="absolute bottom-32 left-16 opacity-20">
          <div className="w-4 h-4 bg-purple-200 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>
        <div className="absolute bottom-20 right-20 opacity-35">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Gradient orbs */}
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex">
        {/* Left side - Registration form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* Registration card */}
            <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
              {/* Header */}
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-4 shadow-lg">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-white mb-2">Créer un compte</h1>
                <p className="text-purple-200">Rejoignez la plateforme PICA</p>
              </div>

              {/* Error message */}
              {error && (
                <div className="mb-4 p-4 bg-red-500/10 border border-red-400/30 rounded-xl flex items-center space-x-3 text-red-300">
                  <AlertCircle className="w-5 h-5 flex-shrink-0" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {/* Success message */}
              {success && (
                <div className="mb-4 p-4 bg-green-500/10 border border-green-400/30 rounded-xl flex items-center space-x-3 text-green-300">
                  <CheckCircle className="w-5 h-5 flex-shrink-0" />
                  <span className="text-sm">{success}</span>
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Name fields */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-purple-200 mb-1">Prénom</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="w-4 h-4 text-purple-400" />
                      </div>
                      <input
                        type="text"
                        value={firstname}
                        onChange={(e) => setFirstname(e.target.value)}
                        className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="Prénom"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-purple-200 mb-1">Nom</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="w-4 h-4 text-purple-400" />
                      </div>
                      <input
                        type="text"
                        value={lastname}
                        onChange={(e) => setLastname(e.target.value)}
                        className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="Nom"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Username field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Nom d'utilisateur</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <UserPlus className="w-4 h-4 text-purple-400" />
                    </div>
                    <input
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="nom_utilisateur"
                      required
                    />
                  </div>
                </div>

                {/* Email field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Adresse e-mail</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="w-4 h-4 text-purple-400" />
                    </div>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                {/* Password field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Mot de passe</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="w-4 h-4 text-purple-400" />
                    </div>
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="••••••••••"
                      required
                    />
                  </div>
                </div>

                {/* Birth date field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Date de naissance</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <CalendarDays className="w-4 h-4 text-purple-400" />
                    </div>
                    <input
                      type="date"
                      value={birthdate}
                      onChange={(e) => setBirthdate(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      required
                    />
                  </div>
                </div>

                {/* Gender field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-1">Genre</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="w-4 h-4 text-purple-400" />
                    </div>
                    <select
                      value={gender}
                      onChange={(e) => setGender(e.target.value)}
                      className="w-full pl-10 pr-3 py-2.5 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      required
                    >
                      <option value="" className="bg-purple-900 text-white">Sélectionnez votre genre</option>
                      <option value="Male" className="bg-purple-900 text-white">Homme</option>
                      <option value="Female" className="bg-purple-900 text-white">Femme</option>
                      <option value="Other" className="bg-purple-900 text-white">Autre</option>
                      <option value="Prefer not to say" className="bg-purple-900 text-white">Préfère ne pas dire</option>
                    </select>
                  </div>
                </div>

                {/* Submit button */}
                <button
                  type="submit"
                  className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                >
                  S'inscrire
                </button>
              </form>

              {/* Login link */}
              <div className="mt-6 text-center">
                <p className="text-purple-200">
                  Vous avez déjà un compte ?{' '}
                  <Link
                    to="/login"
                    className="text-purple-300 hover:text-white font-medium transition-colors"
                  >
                    Se connecter
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Illustration */}
        <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-12">
          <div className="relative">
            {/* 3D Shield illustration */}
            <div className="relative transform hover:scale-105 transition-transform duration-500">
              {/* Main shield */}
              <div className="w-80 h-96 relative">
                {/* Shield base */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500 via-purple-600 to-purple-800 rounded-t-full rounded-b-lg shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                  {/* Shield pattern */}
                  <div className="absolute inset-4 border-2 border-purple-300/30 rounded-t-full rounded-b-lg">
                    <div className="absolute inset-4 border border-purple-300/20 rounded-t-full rounded-b-lg">
                      {/* Center emblem */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div className="w-16 h-16 bg-gradient-to-br from-white to-purple-100 rounded-full flex items-center justify-center shadow-lg">
                          <Shield className="w-8 h-8 text-purple-600" />
                        </div>
                      </div>

                      {/* Decorative lines */}
                      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-20 h-0.5 bg-purple-300/50"></div>
                      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-purple-300/50"></div>
                    </div>
                  </div>
                </div>

                {/* Floating security icons */}
                <div className="absolute -top-8 -left-8 animate-float">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="absolute -top-4 -right-12 animate-float" style={{ animationDelay: '1s' }}>
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                    <Lock className="w-5 h-5 text-white" />
                  </div>
                </div>
                <div className="absolute -bottom-6 -right-8 animate-float" style={{ animationDelay: '2s' }}>
                  <div className="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                    <UserPlus className="w-7 h-7 text-white" />
                  </div>
                </div>
                <div className="absolute bottom-1/3 -left-12 animate-float" style={{ animationDelay: '0.5s' }}>
                  <div className="w-11 h-11 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                    <Mail className="w-5 h-5 text-white" />
                  </div>
                </div>
              </div>
            </div>

            {/* Glowing effect */}
            <div className="absolute inset-0 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
