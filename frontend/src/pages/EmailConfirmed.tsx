import React, { useEffect, useState } from 'react';
import { CheckCircle, XCircle, Loader, Shield, Mail } from 'lucide-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import axios from 'axios';

export default function EmailConfirmed() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('');

  useEffect(() => {
    const confirmEmail = async () => {
      const token = searchParams.get('token');

      if (!token) {
        setStatus('error');
        setMessage('Token de confirmation manquant');
        return;
      }

      try {
        const response = await axios.get(`http://localhost:5000/auth/confirm-email?token=${token}`);
        setStatus('success');
        setMessage(response.data.msg || 'Email confirmé avec succès !');

        // Rediriger vers la page de connexion après 3 secondes
        setTimeout(() => navigate('/login'), 3000);
      } catch (error: any) {
        setStatus('error');
        setMessage(error?.response?.data?.msg || 'Erreur lors de la confirmation de l\'email');
      }
    };

    confirmEmail();
  }, [searchParams, navigate]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Animated background waves */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="none">
            <defs>
              <linearGradient id="wave-gradient-confirm" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#A855F7" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#7C3AED" stopOpacity="0.1" />
              </linearGradient>
            </defs>
            <path d="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z" fill="url(#wave-gradient-confirm)">
              <animate attributeName="d"
                values="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z;
                        M0,300 C300,500 600,100 1200,400 L1200,800 L0,800 Z;
                        M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z"
                dur="8s" repeatCount="indefinite" />
            </path>
          </svg>
        </div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 opacity-30">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
        </div>
        <div className="absolute top-40 right-32 opacity-25">
          <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
        <div className="absolute bottom-32 left-16 opacity-20">
          <div className="w-4 h-4 bg-purple-200 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>
        <div className="absolute bottom-20 right-20 opacity-35">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Gradient orbs */}
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Confirmation card */}
          <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl text-center">

            {/* Loading state */}
            {status === 'loading' && (
              <>
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-6 shadow-lg">
                  <Loader className="w-10 h-10 text-white animate-spin" />
                </div>
                <h1 className="text-3xl font-bold text-white mb-4">Confirmation en cours...</h1>
                <p className="text-purple-200">Veuillez patienter pendant que nous vérifions votre email</p>

                {/* Loading animation */}
                <div className="mt-6 flex justify-center space-x-1">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </>
            )}

            {/* Success state */}
            {status === 'success' && (
              <>
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-lg">
                  <CheckCircle className="w-10 h-10 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-white mb-4">Email confirmé !</h1>
                <p className="text-purple-200 mb-6">{message}</p>

                {/* Success illustration */}
                <div className="relative mb-6">
                  {/* Giant Success Shield */}
                  <div className="relative w-64 h-48 mx-auto flex items-center justify-center">
                    <div className="relative">
                      {/* Main Giant Shield */}
                      <div className="w-48 h-56 bg-gradient-to-br from-green-500 via-emerald-600 to-green-700 rounded-t-full rounded-b-2xl shadow-[0_0_60px_rgba(34,197,94,0.6)] border-4 border-green-300/30">
                        {/* Inner shield glow */}
                        <div className="absolute inset-4 bg-gradient-to-br from-green-400/20 via-emerald-400/10 to-transparent rounded-t-full rounded-b-2xl"></div>

                        {/* Shield pattern */}
                        <div className="absolute inset-6 border-2 border-green-300/20 rounded-t-full rounded-b-2xl">
                          <div className="absolute inset-4 border border-green-300/10 rounded-t-full rounded-b-2xl"></div>
                        </div>

                        {/* Central massive check icon */}
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-gradient-to-br from-white via-green-100 to-emerald-200 rounded-2xl flex items-center justify-center shadow-[0_0_30px_rgba(34,197,94,0.8)] animate-pulse">
                          <svg className="w-12 h-12 text-green-600 drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={4} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>

                        {/* Decorative elements */}
                        <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-20 h-1.5 bg-gradient-to-r from-transparent via-green-200/60 to-transparent rounded-full"></div>
                        <div className="absolute top-12 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-green-200/40 to-transparent rounded-full"></div>
                        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-18 h-1 bg-gradient-to-r from-transparent via-green-200/50 to-transparent rounded-full"></div>
                      </div>

                      {/* Floating success icons around the shield */}
                      <div className="absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-xl flex items-center justify-center shadow-[0_0_20px_rgba(250,204,21,0.6)] animate-float">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </div>

                      <div className="absolute -top-2 -right-8 w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-600 rounded-lg flex items-center justify-center shadow-[0_0_18px_rgba(34,197,94,0.6)] animate-float" style={{ animationDelay: '1s' }}>
                        <CheckCircle className="w-5 h-5 text-white" />
                      </div>

                      <div className="absolute -bottom-6 -right-6 w-14 h-14 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-xl flex items-center justify-center shadow-[0_0_22px_rgba(6,182,212,0.6)] animate-float" style={{ animationDelay: '2s' }}>
                        <Shield className="w-8 h-8 text-white" />
                      </div>

                      <div className="absolute bottom-1/4 -left-10 w-11 h-11 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center shadow-[0_0_20px_rgba(168,85,247,0.6)] animate-float" style={{ animationDelay: '0.5s' }}>
                        <Mail className="w-6 h-6 text-white" />
                      </div>

                      {/* Rotating energy rings */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 border-2 border-green-300/20 rounded-full animate-spin" style={{ animationDuration: '25s' }}>
                        <div className="absolute inset-8 border border-emerald-300/15 rounded-full animate-spin" style={{ animationDuration: '18s', animationDirection: 'reverse' }}></div>
                      </div>

                      {/* Glowing base */}
                      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-56 h-6 bg-gradient-to-r from-transparent via-green-400/60 to-transparent rounded-full blur-xl"></div>
                    </div>

                    {/* Background particles */}
                    <div className="absolute top-8 left-8 w-2 h-2 bg-green-400 rounded-full animate-ping shadow-[0_0_10px_rgba(34,197,94,0.8)]"></div>
                    <div className="absolute top-12 right-12 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-ping shadow-[0_0_8px_rgba(250,204,21,0.8)]" style={{ animationDelay: '0.5s' }}></div>
                    <div className="absolute bottom-12 left-12 w-3 h-3 bg-emerald-300 rounded-full animate-ping shadow-[0_0_12px_rgba(110,231,183,0.8)]" style={{ animationDelay: '1s' }}></div>
                    <div className="absolute bottom-8 right-8 w-2 h-2 bg-cyan-400 rounded-full animate-ping shadow-[0_0_10px_rgba(6,182,212,0.8)]" style={{ animationDelay: '1.5s' }}></div>
                  </div>
                </div>

                <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4 mb-6">
                  <p className="text-sm text-green-300">
                    Redirection vers la page de connexion dans 3 secondes...
                  </p>
                </div>

                <button
                  onClick={() => navigate('/login')}
                  className="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                >
                  Aller à la connexion
                </button>
              </>
            )}

            {/* Error state */}
            {status === 'error' && (
              <>
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-full mb-6 shadow-lg">
                  <XCircle className="w-10 h-10 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-white mb-4">Erreur de confirmation</h1>

                <div className="bg-red-500/10 border border-red-400/30 rounded-xl p-4 mb-6">
                  <p className="text-sm text-red-300">{message}</p>
                </div>

                {/* Error illustration */}
                <div className="relative mb-6">
                  {/* 3D Error scene */}
                  <div className="relative w-64 h-48 mx-auto">
                    {/* Background glow */}
                    <div className="absolute inset-0 bg-gradient-to-br from-red-400/20 to-purple-400/20 rounded-2xl blur-xl"></div>

                    {/* Main error scene */}
                    <div className="relative z-10 w-full h-full">
                      {/* Broken email envelope */}
                      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-32 h-20">
                        {/* Envelope pieces */}
                        <div className="absolute bottom-0 left-2 w-12 h-16 bg-gradient-to-br from-purple-500 to-purple-700 rounded-lg shadow-2xl transform -rotate-12">
                          <div className="absolute inset-2 space-y-1">
                            <div className="h-0.5 bg-purple-200/50 rounded w-full"></div>
                            <div className="h-0.5 bg-purple-200/50 rounded w-2/3"></div>
                          </div>
                        </div>
                        <div className="absolute bottom-0 right-2 w-12 h-16 bg-gradient-to-br from-purple-600 to-purple-800 rounded-lg shadow-2xl transform rotate-12">
                          <div className="absolute inset-2 space-y-1">
                            <div className="h-0.5 bg-purple-200/50 rounded w-full"></div>
                            <div className="h-0.5 bg-purple-200/50 rounded w-1/2"></div>
                          </div>
                        </div>

                        {/* Scattered paper pieces */}
                        <div className="absolute top-2 left-8 w-3 h-3 bg-purple-300 rounded transform rotate-45 animate-pulse"></div>
                        <div className="absolute top-4 right-8 w-2 h-2 bg-purple-400 rounded transform -rotate-12 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                      </div>

                      {/* Floating error symbol */}
                      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-gradient-to-br from-red-500 to-red-700 rounded-full flex items-center justify-center shadow-2xl animate-pulse">
                        <XCircle className="w-8 h-8 text-white" />
                        {/* Error glow effect */}
                        <div className="absolute inset-0 bg-red-500/30 rounded-full blur-lg animate-pulse"></div>
                      </div>

                      {/* Warning particles */}
                      <div className="absolute top-8 left-8 w-2 h-2 bg-red-400 rounded-full animate-ping"></div>
                      <div className="absolute top-12 right-8 w-1 h-1 bg-orange-400 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                      <div className="absolute bottom-12 left-12 w-1.5 h-1.5 bg-red-300 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
                      <div className="absolute bottom-16 right-12 w-1 h-1 bg-orange-300 rounded-full animate-ping" style={{ animationDelay: '1.5s' }}></div>

                      {/* Error waves */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div className="w-48 h-48 border-2 border-red-300/20 rounded-full animate-ping"></div>
                        <div className="absolute inset-4 border border-red-300/10 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => navigate('/login')}
                  className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                >
                  Retour à la connexion
                </button>
              </>
            )}

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-xs text-purple-400">
                © PICA - Plateforme de cybersécurité
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
