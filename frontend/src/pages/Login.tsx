import React, { useState } from 'react';
import { Mail, Lock, AlertCircle, Eye, EyeOff, Shield, CheckCircle } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    try {
      const res = await axios.post('http://localhost:5000/auth/login', {
        email,
        password,
        rememberMe,
      });
      localStorage.setItem('token', res.data.token);
      navigate('/dashboard');
    } catch (err: any) {
      const msg = err?.response?.data?.msg || 'Erreur lors de la connexion';
      setError(msg);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Floating shields */}
        <div className="absolute top-20 left-20 opacity-20">
          <Shield className="w-16 h-16 text-purple-300 animate-pulse" />
        </div>
        <div className="absolute top-40 right-32 opacity-15">
          <Shield className="w-12 h-12 text-purple-400 animate-pulse" style={{ animationDelay: '1s' }} />
        </div>
        <div className="absolute bottom-32 left-16 opacity-10">
          <Shield className="w-20 h-20 text-purple-200 animate-pulse" style={{ animationDelay: '2s' }} />
        </div>
        <div className="absolute bottom-20 right-20 opacity-25">
          <Shield className="w-14 h-14 text-purple-300 animate-pulse" style={{ animationDelay: '0.5s' }} />
        </div>

        {/* Gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex">
        {/* Left side - Illustration */}
        <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-12">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Giant Cybersecurity Shield */}
            <div className="relative">
              {/* Main Giant Shield */}
              <div className="w-80 h-96 bg-gradient-to-br from-purple-500 via-purple-600 to-indigo-700 rounded-t-full rounded-b-3xl shadow-[0_0_80px_rgba(139,92,246,0.6)] border-4 border-purple-300/30">
                {/* Inner shield glow */}
                <div className="absolute inset-6 bg-gradient-to-br from-purple-400/20 via-pink-400/10 to-transparent rounded-t-full rounded-b-3xl"></div>

                {/* Shield pattern */}
                <div className="absolute inset-8 border-2 border-purple-300/20 rounded-t-full rounded-b-3xl">
                  <div className="absolute inset-6 border border-purple-300/10 rounded-t-full rounded-b-3xl"></div>
                </div>

                {/* Central massive lock icon */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-[0_0_40px_rgba(6,182,212,0.8)] animate-pulse">
                  <Shield className="w-20 h-20 text-white drop-shadow-lg" />
                </div>

                {/* Decorative elements */}
                <div className="absolute top-12 left-1/2 transform -translate-x-1/2 w-32 h-2 bg-gradient-to-r from-transparent via-purple-200/60 to-transparent rounded-full"></div>
                <div className="absolute top-16 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-purple-200/40 to-transparent rounded-full"></div>
                <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 w-28 h-1.5 bg-gradient-to-r from-transparent via-purple-200/50 to-transparent rounded-full"></div>
              </div>

              {/* Floating security icons around the shield */}
              <div className="absolute -top-8 -left-8 w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-600 rounded-2xl flex items-center justify-center shadow-[0_0_30px_rgba(34,197,94,0.6)] animate-float">
                <CheckCircle className="w-12 h-12 text-white" />
              </div>

              <div className="absolute -top-4 -right-12 w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-600 rounded-xl flex items-center justify-center shadow-[0_0_25px_rgba(59,130,246,0.6)] animate-float" style={{ animationDelay: '1s' }}>
                <Lock className="w-10 h-10 text-white" />
              </div>

              <div className="absolute -bottom-8 -right-8 w-24 h-24 bg-gradient-to-br from-orange-400 to-red-500 rounded-2xl flex items-center justify-center shadow-[0_0_35px_rgba(251,146,60,0.6)] animate-float" style={{ animationDelay: '2s' }}>
                <Eye className="w-14 h-14 text-white" />
              </div>

              <div className="absolute bottom-1/3 -left-16 w-18 h-18 bg-gradient-to-br from-pink-400 to-purple-500 rounded-xl flex items-center justify-center shadow-[0_0_28px_rgba(236,72,153,0.6)] animate-float" style={{ animationDelay: '0.5s' }}>
                <Mail className="w-10 h-10 text-white" />
              </div>

              {/* Rotating energy rings */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] border-4 border-purple-300/20 rounded-full animate-spin" style={{ animationDuration: '30s' }}>
                <div className="absolute inset-12 border-2 border-cyan-300/15 rounded-full animate-spin" style={{ animationDuration: '20s', animationDirection: 'reverse' }}></div>
                <div className="absolute inset-24 border border-green-300/10 rounded-full animate-spin" style={{ animationDuration: '15s' }}></div>
              </div>

              {/* Glowing base */}
              <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 w-96 h-8 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent rounded-full blur-2xl"></div>
            </div>

            {/* Background particles */}
            <div className="absolute top-16 left-16 w-4 h-4 bg-purple-400 rounded-full animate-ping shadow-[0_0_15px_rgba(168,85,247,0.8)]"></div>
            <div className="absolute top-32 right-20 w-3 h-3 bg-cyan-400 rounded-full animate-ping shadow-[0_0_12px_rgba(6,182,212,0.8)]" style={{ animationDelay: '0.5s' }}></div>
            <div className="absolute bottom-32 left-24 w-5 h-5 bg-green-400 rounded-full animate-ping shadow-[0_0_18px_rgba(34,197,94,0.8)]" style={{ animationDelay: '1s' }}></div>
            <div className="absolute bottom-20 right-16 w-3 h-3 bg-pink-400 rounded-full animate-ping shadow-[0_0_12px_rgba(236,72,153,0.8)]" style={{ animationDelay: '1.5s' }}></div>
          </div>
        </div>

        {/* Right side - Login form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* Login card */}
            <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
              {/* Header */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-4 shadow-lg">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-white mb-2">Connexion</h1>
                <p className="text-purple-200">Accédez à votre espace PICA</p>
              </div>

              {/* Error message */}
              {error && (
                <div className="mb-6 p-4 bg-red-500/10 border border-red-400/30 rounded-xl flex items-center space-x-3 text-red-300">
                  <AlertCircle className="w-5 h-5 flex-shrink-0" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-2">
                    Adresse e-mail
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Mail className="w-5 h-5 text-purple-400" />
                    </div>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-12 pr-4 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                {/* Password field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-2">
                    Mot de passe
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Lock className="w-5 h-5 text-purple-400" />
                    </div>
                    <input
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-12 pr-12 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="••••••••••"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-4 flex items-center text-purple-400 hover:text-purple-300 transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>

                  {/* Forgot password link */}
                  <div className="mt-2">
                    <Link
                      to="/forgot-password"
                      className="text-sm text-purple-300 hover:text-white transition-colors"
                    >
                      Mot de passe oublié ?
                    </Link>
                  </div>
                </div>

                {/* Remember me */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="rememberMe"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="w-4 h-4 text-purple-600 bg-purple-800/30 border-purple-500/30 rounded focus:ring-purple-500 focus:ring-2"
                  />
                  <label htmlFor="rememberMe" className="ml-3 text-sm text-purple-200">
                    Se souvenir de moi (24h)
                  </label>
                </div>

                {/* Submit button */}
                <button
                  type="submit"
                  className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                >
                  Se connecter
                </button>
              </form>

              {/* Register link */}
              <div className="mt-8 text-center">
                <p className="text-purple-200">
                  Vous n'avez pas de compte ?{' '}
                  <Link
                    to="/register"
                    className="text-purple-300 hover:text-white font-medium transition-colors"
                  >
                    Créer un compte
                  </Link>
                </p>
              </div>

              {/* Footer */}
              <div className="mt-8 text-center">
                <p className="text-xs text-purple-400">
                  © PICA - Plateforme de cybersécurité
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
