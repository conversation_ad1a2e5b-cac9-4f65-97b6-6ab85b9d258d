import React, { useState } from 'react';
import { Mail, Lock, AlertCircle, Eye, EyeOff, Shield } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    try {
      const res = await axios.post('http://localhost:5000/auth/login', {
        email,
        password,
        rememberMe,
      });
      localStorage.setItem('token', res.data.token);
      navigate('/dashboard');
    } catch (err: any) {
      const msg = err?.response?.data?.msg || 'Erreur lors de la connexion';
      setError(msg);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Floating shields */}
        <div className="absolute top-20 left-20 opacity-20">
          <Shield className="w-16 h-16 text-purple-300 animate-pulse" />
        </div>
        <div className="absolute top-40 right-32 opacity-15">
          <Shield className="w-12 h-12 text-purple-400 animate-pulse" style={{ animationDelay: '1s' }} />
        </div>
        <div className="absolute bottom-32 left-16 opacity-10">
          <Shield className="w-20 h-20 text-purple-200 animate-pulse" style={{ animationDelay: '2s' }} />
        </div>
        <div className="absolute bottom-20 right-20 opacity-25">
          <Shield className="w-14 h-14 text-purple-300 animate-pulse" style={{ animationDelay: '0.5s' }} />
        </div>

        {/* Gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex">
        {/* Left side - Illustration */}
        <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-12">
          <div className="relative w-96 h-96">
            {/* 3D Isometric Shield with Orbiting Elements */}
            <div className="relative w-full h-full">

              {/* Central 3D Shield */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                {/* Main Shield Body - 3D Effect */}
                <div className="relative w-48 h-56 transform-gpu" style={{
                  transform: 'perspective(1000px) rotateX(15deg) rotateY(-15deg)',
                  filter: 'drop-shadow(0 20px 40px rgba(139, 92, 246, 0.4))'
                }}>
                  {/* Shield Back Layer */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-800 via-purple-700 to-purple-900 rounded-t-full rounded-b-lg transform translate-x-2 translate-y-2 opacity-60"></div>

                  {/* Shield Middle Layer */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-purple-500 to-purple-700 rounded-t-full rounded-b-lg transform translate-x-1 translate-y-1 opacity-80"></div>

                  {/* Shield Front Layer */}
                  <div className="relative w-full h-full bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600 rounded-t-full rounded-b-lg shadow-2xl">
                    {/* Inner Glow */}
                    <div className="absolute inset-4 bg-gradient-to-br from-purple-300/30 via-pink-300/20 to-purple-400/30 rounded-t-full rounded-b-lg"></div>

                    {/* Shield Border */}
                    <div className="absolute inset-2 border-2 border-purple-300/40 rounded-t-full rounded-b-lg"></div>

                    {/* Central Check Icon */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>

                    {/* Shield Highlights */}
                    <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-transparent via-purple-200/60 to-transparent rounded-full"></div>
                    <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-transparent via-purple-200/40 to-transparent rounded-full"></div>
                  </div>

                  {/* 3D Depth Lines */}
                  <div className="absolute -right-2 top-2 bottom-2 w-2 bg-gradient-to-b from-purple-800 to-purple-900 rounded-r-lg opacity-60"></div>
                  <div className="absolute -bottom-2 left-2 right-2 h-2 bg-gradient-to-r from-purple-800 to-purple-900 rounded-b-lg opacity-60"></div>
                </div>

                {/* Glowing Base */}
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-32 h-4 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent rounded-full blur-lg"></div>
              </div>

              {/* Orbiting Small Shields */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                {/* Orbit Ring 1 */}
                <div className="relative w-80 h-80 animate-spin" style={{ animationDuration: '20s' }}>
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div
                      key={`orbit1-${i}`}
                      className="absolute w-8 h-10 transform-gpu"
                      style={{
                        top: '50%',
                        left: '50%',
                        transform: `translate(-50%, -50%) rotate(${i * 60}deg) translateY(-140px) rotate(-${i * 60}deg) perspective(500px) rotateX(10deg) rotateY(-10deg)`,
                      }}
                    >
                      <div className="w-full h-full bg-gradient-to-br from-purple-400 to-purple-600 rounded-t-full rounded-b-sm shadow-lg">
                        <div className="absolute inset-1 bg-gradient-to-br from-purple-300/40 to-transparent rounded-t-full rounded-b-sm"></div>
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-pink-400 rounded-full"></div>
                      </div>
                      <div className="absolute inset-0 bg-purple-400/30 rounded-t-full rounded-b-sm blur-sm"></div>
                    </div>
                  ))}
                </div>

                {/* Orbit Ring 2 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}>
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div
                      key={`orbit2-${i}`}
                      className="absolute w-6 h-8 transform-gpu"
                      style={{
                        top: '50%',
                        left: '50%',
                        transform: `translate(-50%, -50%) rotate(${i * 90}deg) translateY(-110px) rotate(-${i * 90}deg) perspective(400px) rotateX(15deg) rotateY(-15deg)`,
                      }}
                    >
                      <div className="w-full h-full bg-gradient-to-br from-pink-400 to-purple-500 rounded-t-full rounded-b-sm shadow-lg">
                        <div className="absolute inset-1 bg-gradient-to-br from-pink-300/40 to-transparent rounded-t-full rounded-b-sm"></div>
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-purple-200 rounded-full"></div>
                      </div>
                      <div className="absolute inset-0 bg-pink-400/30 rounded-t-full rounded-b-sm blur-sm"></div>
                    </div>
                  ))}
                </div>

                {/* Orbit Ring 3 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 animate-spin" style={{ animationDuration: '12s' }}>
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div
                      key={`orbit3-${i}`}
                      className="absolute w-5 h-6 transform-gpu"
                      style={{
                        top: '50%',
                        left: '50%',
                        transform: `translate(-50%, -50%) rotate(${i * 120}deg) translateY(-80px) rotate(-${i * 120}deg) perspective(300px) rotateX(20deg) rotateY(-20deg)`,
                      }}
                    >
                      <div className="w-full h-full bg-gradient-to-br from-cyan-400 to-purple-500 rounded-t-full rounded-b-sm shadow-lg">
                        <div className="absolute inset-1 bg-gradient-to-br from-cyan-300/40 to-transparent rounded-t-full rounded-b-sm"></div>
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-white rounded-full"></div>
                      </div>
                      <div className="absolute inset-0 bg-cyan-400/30 rounded-t-full rounded-b-sm blur-sm"></div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Floating Particles */}
              <div className="absolute top-12 left-12 w-2 h-2 bg-purple-400 rounded-full animate-ping shadow-[0_0_10px_rgba(168,85,247,0.8)]"></div>
              <div className="absolute top-20 right-16 w-1.5 h-1.5 bg-pink-400 rounded-full animate-ping shadow-[0_0_8px_rgba(236,72,153,0.8)]" style={{ animationDelay: '0.5s' }}></div>
              <div className="absolute bottom-24 left-20 w-3 h-3 bg-purple-300 rounded-full animate-ping shadow-[0_0_12px_rgba(196,181,253,0.8)]" style={{ animationDelay: '1s' }}></div>
              <div className="absolute bottom-16 right-12 w-2 h-2 bg-cyan-400 rounded-full animate-ping shadow-[0_0_10px_rgba(6,182,212,0.8)]" style={{ animationDelay: '1.5s' }}></div>

              {/* Background Glow */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-radial from-purple-500/20 via-purple-600/10 to-transparent rounded-full blur-3xl animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Right side - Login form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* Login card */}
            <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
              {/* Header */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-4 shadow-lg">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-white mb-2">Connexion</h1>
                <p className="text-purple-200">Accédez à votre espace PICA</p>
              </div>

              {/* Error message */}
              {error && (
                <div className="mb-6 p-4 bg-red-500/10 border border-red-400/30 rounded-xl flex items-center space-x-3 text-red-300">
                  <AlertCircle className="w-5 h-5 flex-shrink-0" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-2">
                    Adresse e-mail
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Mail className="w-5 h-5 text-purple-400" />
                    </div>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-12 pr-4 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                {/* Password field */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-2">
                    Mot de passe
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Lock className="w-5 h-5 text-purple-400" />
                    </div>
                    <input
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-12 pr-12 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="••••••••••"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-4 flex items-center text-purple-400 hover:text-purple-300 transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>

                  {/* Forgot password link */}
                  <div className="mt-2">
                    <Link
                      to="/forgot-password"
                      className="text-sm text-purple-300 hover:text-white transition-colors"
                    >
                      Mot de passe oublié ?
                    </Link>
                  </div>
                </div>

                {/* Remember me */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="rememberMe"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="w-4 h-4 text-purple-600 bg-purple-800/30 border-purple-500/30 rounded focus:ring-purple-500 focus:ring-2"
                  />
                  <label htmlFor="rememberMe" className="ml-3 text-sm text-purple-200">
                    Se souvenir de moi (24h)
                  </label>
                </div>

                {/* Submit button */}
                <button
                  type="submit"
                  className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                >
                  Se connecter
                </button>
              </form>

              {/* Register link */}
              <div className="mt-8 text-center">
                <p className="text-purple-200">
                  Vous n'avez pas de compte ?{' '}
                  <Link
                    to="/register"
                    className="text-purple-300 hover:text-white font-medium transition-colors"
                  >
                    Créer un compte
                  </Link>
                </p>
              </div>

              {/* Footer */}
              <div className="mt-8 text-center">
                <p className="text-xs text-purple-400">
                  © PICA - Plateforme de cybersécurité
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
