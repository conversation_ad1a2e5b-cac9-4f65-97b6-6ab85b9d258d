import React, { useState } from 'react';
import { Mail, Lock, AlertCircle, ShieldCheck } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    try {
      const res = await axios.post('http://localhost:5000/auth/login', { email, password });
      localStorage.setItem('token', res.data.token);
      navigate('/dashboard');
    } catch (err: any) {
      const msg = err?.response?.data?.msg || 'Erreur lors de la connexion';
      setError(msg);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#2d004d] to-[#5f1a7c] flex items-center justify-center relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/background-waves.svg')] bg-cover opacity-10 pointer-events-none" />
      <div className="flex flex-col md:flex-row w-full max-w-6xl mx-auto items-center justify-between px-6 md:px-12">

        {/* Section graphique */}
        <div className="hidden md:flex w-1/2 justify-center items-center">
          <img src="/cyber-shield.png" alt="Cybershield" className="w-80 animate-pulse drop-shadow-lg" />
        </div>

        {/* Formulaire */}
        <div className="w-full md:w-1/2 bg-[#1a1a2e]/80 backdrop-blur-md rounded-lg p-8 shadow-2xl text-white">
          <div className="text-center mb-8">
            <ShieldCheck className="w-12 h-12 text-violet-400 mx-auto mb-2" />
            <h2 className="text-3xl font-bold">Connexion</h2>
            <p className="text-sm text-violet-200">Accédez à votre espace PICA</p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-500/10 border border-red-400 rounded-md flex gap-2 items-center text-red-300">
              <AlertCircle size={16} />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <form className="space-y-4" onSubmit={handleSubmit}>
            <div>
              <label className="block text-sm mb-1">Adresse e-mail</label>
              <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                <Mail className="w-4 h-4 text-violet-400 mr-2" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-transparent text-white focus:outline-none"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm mb-1">Mot de passe</label>
              <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
                <Lock className="w-4 h-4 text-violet-400 mr-2" />
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-transparent text-white focus:outline-none"
                  placeholder="********"
                  required
                />
              </div>
            </div>

            <button
              type="submit"
              className="w-full py-2 rounded-md bg-gradient-to-r from-violet-500 to-indigo-500 hover:opacity-90 transition duration-200 text-white font-semibold"
            >
              Se connecter
            </button>
          </form>

          <p className="text-center text-sm text-violet-300 mt-6">
            Vous n'avez pas de compte ? <Link to="/register" className="underline hover:text-white">Créer un compte</Link>
          </p>
        </div>
      </div>
    </div>
  );
}
