import React, { useState } from 'react';
import { Mail, ArrowLeft, Shield, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import axios from 'axios';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus('loading');
    setMessage('');

    try {
      const response = await axios.post('http://localhost:5000/auth/forgot-password', { email });
      setStatus('success');
      setMessage(response.data.msg || 'Instructions de réinitialisation envoyées par email');
    } catch (error: any) {
      setStatus('error');
      setMessage(error?.response?.data?.msg || 'Erreur lors de l\'envoi des instructions');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Animated background waves */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="none">
            <defs>
              <linearGradient id="wave-gradient-forgot" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#A855F7" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#7C3AED" stopOpacity="0.1" />
              </linearGradient>
            </defs>
            <path d="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z" fill="url(#wave-gradient-forgot)">
              <animate attributeName="d" 
                values="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z;
                        M0,300 C300,500 600,100 1200,400 L1200,800 L0,800 Z;
                        M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z"
                dur="8s" repeatCount="indefinite" />
            </path>
          </svg>
        </div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 opacity-30">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
        </div>
        <div className="absolute top-40 right-32 opacity-25">
          <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
        <div className="absolute bottom-32 left-16 opacity-20">
          <div className="w-4 h-4 bg-purple-200 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>
        <div className="absolute bottom-20 right-20 opacity-35">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Gradient orbs */}
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Forgot Password card */}
          <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
            
            {/* Back button */}
            <div className="mb-6">
              <Link
                to="/login"
                className="inline-flex items-center text-purple-300 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Retour à la connexion
              </Link>
            </div>

            {status === 'idle' || status === 'loading' ? (
              <>
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-4 shadow-lg">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-2">Mot de passe oublié</h1>
                  <p className="text-purple-200">Entrez votre email pour recevoir les instructions de réinitialisation</p>
                </div>

                {/* Error message */}
                {status === 'error' && (
                  <div className="mb-6 p-4 bg-red-500/10 border border-red-400/30 rounded-xl flex items-center space-x-3 text-red-300">
                    <AlertCircle className="w-5 h-5 flex-shrink-0" />
                    <span className="text-sm">{message}</span>
                  </div>
                )}

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Email field */}
                  <div>
                    <label className="block text-sm font-medium text-purple-200 mb-2">
                      Adresse e-mail
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <Mail className="w-5 h-5 text-purple-400" />
                      </div>
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full pl-12 pr-4 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="<EMAIL>"
                        required
                        disabled={status === 'loading'}
                      />
                    </div>
                  </div>

                  {/* Submit button */}
                  <button
                    type="submit"
                    disabled={status === 'loading'}
                    className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                  >
                    {status === 'loading' ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Envoi en cours...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Send className="w-5 h-5 mr-2" />
                        Envoyer les instructions
                      </div>
                    )}
                  </button>
                </form>
              </>
            ) : (
              <>
                {/* Success state */}
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-lg">
                    <CheckCircle className="w-10 h-10 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-4">Email envoyé !</h1>
                  
                  <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4 mb-6">
                    <p className="text-sm text-green-300">{message}</p>
                  </div>

                  <div className="text-purple-200 mb-6">
                    <p className="mb-2">Vérifiez votre boîte email et cliquez sur le lien de réinitialisation.</p>
                    <p className="text-sm">Le lien expire dans 10 minutes.</p>
                  </div>

                  {/* Illustration */}
                  <div className="relative mb-6">
                    {/* 3D Email sending scene */}
                    <div className="relative w-64 h-48 mx-auto">
                      {/* Background glow */}
                      <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-purple-400/20 rounded-2xl blur-xl"></div>

                      {/* Main email sending scene */}
                      <div className="relative z-10 w-full h-full">
                        {/* Email server */}
                        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-24 h-32 bg-gradient-to-t from-purple-700 to-purple-500 rounded-lg shadow-2xl">
                          {/* Server lights */}
                          <div className="absolute top-2 left-2 right-2 space-y-1">
                            <div className="h-1 bg-green-400 rounded animate-pulse"></div>
                            <div className="h-1 bg-blue-400 rounded animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                            <div className="h-1 bg-cyan-400 rounded animate-pulse" style={{ animationDelay: '1s' }}></div>
                          </div>

                          {/* Server vents */}
                          <div className="absolute bottom-2 left-2 right-2 grid grid-cols-4 gap-0.5">
                            {Array.from({ length: 8 }).map((_, i) => (
                              <div key={i} className="h-1 bg-purple-300/30 rounded-sm"></div>
                            ))}
                          </div>
                        </div>

                        {/* Flying email */}
                        <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-16 h-12 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg shadow-lg animate-bounce">
                          <div className="absolute inset-2 bg-gradient-to-br from-cyan-300/30 to-transparent rounded">
                            <Send className="w-full h-full text-white p-1" />
                          </div>
                          {/* Email trail */}
                          <div className="absolute -right-2 top-1/2 transform -translate-y-1/2 w-8 h-0.5 bg-gradient-to-r from-cyan-400 to-transparent"></div>
                          <div className="absolute -right-4 top-1/2 transform -translate-y-1/2 w-6 h-0.5 bg-gradient-to-r from-cyan-300 to-transparent" style={{ animationDelay: '0.2s' }}></div>
                        </div>

                        {/* Floating particles */}
                        <div className="absolute top-12 left-8 w-2 h-2 bg-green-300 rounded-full animate-ping"></div>
                        <div className="absolute top-16 right-8 w-1 h-1 bg-cyan-300 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                        <div className="absolute bottom-16 left-12 w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
                        <div className="absolute bottom-20 right-12 w-1 h-1 bg-blue-400 rounded-full animate-ping" style={{ animationDelay: '1.5s' }}></div>

                        {/* Signal waves */}
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                          <div className="w-48 h-48 border-2 border-cyan-300/20 rounded-full animate-ping"></div>
                          <div className="absolute inset-4 border border-cyan-300/10 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      setStatus('idle');
                      setEmail('');
                      setMessage('');
                    }}
                    className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                  >
                    Renvoyer un email
                  </button>
                </div>
              </>
            )}

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-xs text-purple-400">
                © PICA - Plateforme de cybersécurité
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
