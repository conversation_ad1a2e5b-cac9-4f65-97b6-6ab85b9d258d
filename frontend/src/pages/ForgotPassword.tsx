import React, { useState } from 'react';
import { Mail, ArrowLeft, Shield, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import axios from 'axios';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus('loading');
    setMessage('');

    try {
      const response = await axios.post('http://localhost:5000/auth/forgot-password', { email });
      setStatus('success');
      setMessage(response.data.msg || 'Instructions de réinitialisation envoyées par email');
    } catch (error: any) {
      setStatus('error');
      setMessage(error?.response?.data?.msg || 'Erreur lors de l\'envoi des instructions');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Animated background waves */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="none">
            <defs>
              <linearGradient id="wave-gradient-forgot" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#A855F7" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#7C3AED" stopOpacity="0.1" />
              </linearGradient>
            </defs>
            <path d="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z" fill="url(#wave-gradient-forgot)">
              <animate attributeName="d" 
                values="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z;
                        M0,300 C300,500 600,100 1200,400 L1200,800 L0,800 Z;
                        M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z"
                dur="8s" repeatCount="indefinite" />
            </path>
          </svg>
        </div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 opacity-30">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
        </div>
        <div className="absolute top-40 right-32 opacity-25">
          <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
        <div className="absolute bottom-32 left-16 opacity-20">
          <div className="w-4 h-4 bg-purple-200 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>
        <div className="absolute bottom-20 right-20 opacity-35">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Gradient orbs */}
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Forgot Password card */}
          <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
            
            {/* Back button */}
            <div className="mb-6">
              <Link
                to="/login"
                className="inline-flex items-center text-purple-300 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Retour à la connexion
              </Link>
            </div>

            {status === 'idle' || status === 'loading' ? (
              <>
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-4 shadow-lg">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-2">Mot de passe oublié</h1>
                  <p className="text-purple-200">Entrez votre email pour recevoir les instructions de réinitialisation</p>
                </div>

                {/* Error message */}
                {status === 'error' && (
                  <div className="mb-6 p-4 bg-red-500/10 border border-red-400/30 rounded-xl flex items-center space-x-3 text-red-300">
                    <AlertCircle className="w-5 h-5 flex-shrink-0" />
                    <span className="text-sm">{message}</span>
                  </div>
                )}

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Email field */}
                  <div>
                    <label className="block text-sm font-medium text-purple-200 mb-2">
                      Adresse e-mail
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <Mail className="w-5 h-5 text-purple-400" />
                      </div>
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full pl-12 pr-4 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="<EMAIL>"
                        required
                        disabled={status === 'loading'}
                      />
                    </div>
                  </div>

                  {/* Submit button */}
                  <button
                    type="submit"
                    disabled={status === 'loading'}
                    className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                  >
                    {status === 'loading' ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Envoi en cours...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Send className="w-5 h-5 mr-2" />
                        Envoyer les instructions
                      </div>
                    )}
                  </button>
                </form>
              </>
            ) : (
              <>
                {/* Success state */}
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-lg">
                    <CheckCircle className="w-10 h-10 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-4">Email envoyé !</h1>
                  
                  <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4 mb-6">
                    <p className="text-sm text-green-300">{message}</p>
                  </div>

                  <div className="text-purple-200 mb-6">
                    <p className="mb-2">Vérifiez votre boîte email et cliquez sur le lien de réinitialisation.</p>
                    <p className="text-sm">Le lien expire dans 10 minutes.</p>
                  </div>

                  {/* Illustration */}
                  <div className="relative mb-6">
                    {/* Giant Email Recovery Shield */}
                    <div className="relative w-64 h-48 mx-auto flex items-center justify-center">
                      <div className="relative">
                        {/* Main Giant Shield */}
                        <div className="w-48 h-56 bg-gradient-to-br from-cyan-500 via-blue-600 to-indigo-700 rounded-t-full rounded-b-2xl shadow-[0_0_60px_rgba(6,182,212,0.6)] border-4 border-cyan-300/30">
                          {/* Inner shield glow */}
                          <div className="absolute inset-4 bg-gradient-to-br from-cyan-400/20 via-blue-400/10 to-transparent rounded-t-full rounded-b-2xl"></div>

                          {/* Shield pattern */}
                          <div className="absolute inset-6 border-2 border-cyan-300/20 rounded-t-full rounded-b-2xl">
                            <div className="absolute inset-4 border border-cyan-300/10 rounded-t-full rounded-b-2xl"></div>
                          </div>

                          {/* Central massive email icon */}
                          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-gradient-to-br from-orange-400 via-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-[0_0_30px_rgba(251,146,60,0.8)] animate-pulse">
                            <Mail className="w-12 h-12 text-white drop-shadow-lg" />
                          </div>

                          {/* Decorative elements */}
                          <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-20 h-1.5 bg-gradient-to-r from-transparent via-cyan-200/60 to-transparent rounded-full"></div>
                          <div className="absolute top-12 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-cyan-200/40 to-transparent rounded-full"></div>
                          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-18 h-1 bg-gradient-to-r from-transparent via-cyan-200/50 to-transparent rounded-full"></div>
                        </div>

                        {/* Floating email recovery icons around the shield */}
                        <div className="absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-xl flex items-center justify-center shadow-[0_0_20px_rgba(250,204,21,0.6)] animate-float">
                          <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clipRule="evenodd" />
                          </svg>
                        </div>

                        <div className="absolute -top-2 -right-8 w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-600 rounded-lg flex items-center justify-center shadow-[0_0_18px_rgba(34,197,94,0.6)] animate-float" style={{ animationDelay: '1s' }}>
                          <Send className="w-5 h-5 text-white" />
                        </div>

                        <div className="absolute -bottom-6 -right-6 w-14 h-14 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center shadow-[0_0_22px_rgba(168,85,247,0.6)] animate-float" style={{ animationDelay: '2s' }}>
                          <Shield className="w-8 h-8 text-white" />
                        </div>

                        <div className="absolute bottom-1/4 -left-10 w-11 h-11 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-lg flex items-center justify-center shadow-[0_0_20px_rgba(59,130,246,0.6)] animate-float" style={{ animationDelay: '0.5s' }}>
                          <Lock className="w-6 h-6 text-white" />
                        </div>

                        {/* Rotating energy rings */}
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 border-2 border-cyan-300/20 rounded-full animate-spin" style={{ animationDuration: '25s' }}>
                          <div className="absolute inset-8 border border-blue-300/15 rounded-full animate-spin" style={{ animationDuration: '18s', animationDirection: 'reverse' }}></div>
                        </div>

                        {/* Glowing base */}
                        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-56 h-6 bg-gradient-to-r from-transparent via-cyan-400/60 to-transparent rounded-full blur-xl"></div>
                      </div>

                      {/* Background particles */}
                      <div className="absolute top-8 left-8 w-2 h-2 bg-cyan-400 rounded-full animate-ping shadow-[0_0_10px_rgba(6,182,212,0.8)]"></div>
                      <div className="absolute top-12 right-12 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping shadow-[0_0_8px_rgba(251,146,60,0.8)]" style={{ animationDelay: '0.5s' }}></div>
                      <div className="absolute bottom-12 left-12 w-3 h-3 bg-blue-300 rounded-full animate-ping shadow-[0_0_12px_rgba(147,197,253,0.8)]" style={{ animationDelay: '1s' }}></div>
                      <div className="absolute bottom-8 right-8 w-2 h-2 bg-purple-400 rounded-full animate-ping shadow-[0_0_10px_rgba(168,85,247,0.8)]" style={{ animationDelay: '1.5s' }}></div>
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      setStatus('idle');
                      setEmail('');
                      setMessage('');
                    }}
                    className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                  >
                    Renvoyer un email
                  </button>
                </div>
              </>
            )}

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-xs text-purple-400">
                © PICA - Plateforme de cybersécurité
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
