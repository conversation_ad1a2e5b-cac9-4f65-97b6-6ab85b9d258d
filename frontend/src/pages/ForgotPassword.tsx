import React, { useState } from 'react';
import { Mail, AlertCircle, ShieldCheck, ArrowLeft, CheckCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import axios from 'axios';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setIsLoading(true);

    try {
      const res = await axios.post('http://localhost:5000/auth/forgot-password', {
        email,
      });
      setSuccess(res.data.msg || 'Lien de réinitialisation envoyé à votre email');
      setEmail(''); // Clear the form
    } catch (err: any) {
      const msg = err?.response?.data?.msg || 'Erreur lors de l\'envoi du lien de réinitialisation';
      setError(msg);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#2d004d] to-[#5f1a7c] px-4">
      <div className="max-w-md w-full bg-[#1a1a2e] rounded-lg p-8 shadow-xl text-white">
        <div className="text-center mb-8">
          <ShieldCheck className="w-12 h-12 text-violet-400 mx-auto mb-2" />
          <h2 className="text-3xl font-bold">Mot de passe oublié</h2>
          <p className="text-sm text-violet-200">
            Entrez votre email pour recevoir un lien de réinitialisation
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-400 rounded-md flex gap-2 items-center text-red-300">
            <AlertCircle size={16} />
            <span className="text-sm">{error}</span>
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-500/10 border border-green-400 rounded-md flex gap-2 items-center text-green-300">
            <CheckCircle size={16} />
            <span className="text-sm">{success}</span>
          </div>
        )}

        <form className="space-y-4" onSubmit={handleSubmit}>
          <div>
            <label className="block text-sm mb-1">Adresse e-mail</label>
            <div className="flex items-center bg-[#2d2d44] border border-violet-500 rounded-md px-3 py-2">
              <Mail className="w-4 h-4 text-violet-400 mr-2" />
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full bg-transparent text-white focus:outline-none"
                placeholder="<EMAIL>"
                required
                disabled={isLoading}
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-2 rounded-md bg-gradient-to-r from-violet-500 to-indigo-500 hover:opacity-90 transition duration-200 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Envoi en cours...' : 'Envoyer le lien de réinitialisation'}
          </button>
        </form>

        <div className="text-center mt-6">
          <Link 
            to="/login" 
            className="inline-flex items-center text-sm text-violet-300 hover:text-white underline"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Retour à la connexion
          </Link>
        </div>

        <p className="mt-8 text-center text-xs text-violet-400">
          © PICA - Plateforme de cybersécurité
        </p>
      </div>
    </div>
  );
}
